<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试撤销/重做功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background: #e9ecef;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>撤销/重做功能测试</h1>

    <div class="test-container">
        <h2>测试说明</h2>
        <p>这个页面用于测试JointJS应用的撤销/重做功能，特别是批量删除操作的撤销功能。</p>
        <p><strong>测试步骤：</strong></p>
        <ol>
            <li>点击"打开主应用"按钮打开JointJS应用</li>
            <li>在主应用中添加3个节点到画布</li>
            <li>使用多选功能选中所有3个节点</li>
            <li>按Delete键或使用删除功能删除选中的节点</li>
            <li>按Ctrl+Z进行撤销操作</li>
            <li>验证所有3个节点是否正确恢复</li>
        </ol>
    </div>

    <div class="test-container">
        <h2>快速测试</h2>
        <button type="button" class="test-button" onclick="openMainApp()">打开主应用</button>
        <button type="button" class="test-button" onclick="checkUndoRedoStatus()">检查撤销/重做状态</button>
        <button type="button" class="test-button" onclick="testBatchDelete()">自动测试批量删除</button>
        <button type="button" class="test-button" onclick="clearLog()">清空日志</button>
    </div>

    <div class="test-container">
        <h2>测试状态</h2>
        <div id="status" class="status">等待测试...</div>
    </div>

    <div class="test-container">
        <h2>测试日志</h2>
        <div id="log" class="log">测试日志将显示在这里...\n</div>
    </div>

    <script>
        let logElement = document.getElementById('log');
        let statusElement = document.getElementById('status');
        let mainAppWindow = null;
        let isMainAppReady = false;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}\n`;
            logElement.textContent += logMessage;
            logElement.scrollTop = logElement.scrollHeight;

            console.log(message);
        }

        function setStatus(message, type = 'info') {
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        function openMainApp() {
            try {
                // 打开主应用并保存窗口引用
                mainAppWindow = window.open('index.html', 'mainApp');

                if (mainAppWindow) {
                    log('已打开主应用窗口，等待应用初始化...');
                    setStatus('主应用正在加载，请稍候...', 'info');

                    // 设置定时器检查主应用是否准备就绪
                    checkMainAppReady();
                } else {
                    throw new Error('无法打开主应用窗口，可能被浏览器阻止');
                }
            } catch (error) {
                log(`打开主应用失败: ${error.message}`, 'error');
                setStatus('打开主应用失败', 'error');
            }
        }

        function checkMainAppReady() {
            let attempts = 0;
            const maxAttempts = 30; // 最多等待30秒

            const checkInterval = setInterval(() => {
                attempts++;

                try {
                    if (mainAppWindow && !mainAppWindow.closed) {
                        // 尝试访问主应用的API
                        if (mainAppWindow.WorkflowAPI && mainAppWindow.workflowApp) {
                            isMainAppReady = true;
                            clearInterval(checkInterval);
                            log('主应用已准备就绪，可以进行测试');
                            setStatus('主应用已准备就绪，可以开始测试', 'success');

                            // 设置消息监听
                            setupMessageListener();
                            return;
                        }
                    } else if (mainAppWindow && mainAppWindow.closed) {
                        clearInterval(checkInterval);
                        log('主应用窗口已关闭');
                        setStatus('主应用窗口已关闭', 'error');
                        mainAppWindow = null;
                        isMainAppReady = false;
                        return;
                    }
                } catch (error) {
                    // 跨域或其他访问错误，继续尝试
                }

                if (attempts >= maxAttempts) {
                    clearInterval(checkInterval);
                    log('等待主应用准备就绪超时，请手动刷新主应用或重新打开');
                    setStatus('主应用加载超时，请重新打开', 'error');
                }
            }, 1000);
        }

        function setupMessageListener() {
            // 监听来自主应用的消息
            window.addEventListener('message', function(event) {
                // 验证消息来源
                if (event.source === mainAppWindow) {
                    handleMainAppMessage(event.data);
                }
            });
        }

        function handleMainAppMessage(data) {
            if (data && data.type) {
                switch (data.type) {
                    case 'app-ready':
                        log('收到主应用就绪信号');
                        isMainAppReady = true;
                        setStatus('主应用已完全加载，可以开始测试', 'success');
                        break;
                    case 'test-result':
                        log(`测试结果: ${data.message}`);
                        setStatus(data.message, data.success ? 'success' : 'error');
                        break;
                    case 'status-response':
                        log('收到状态检查响应');
                        if (data.status) {
                            displayStatusInfo(data.status);
                        }
                        break;
                    default:
                        log(`收到未知消息类型: ${data.type}`);
                }
            }
        }

        function displayStatusInfo(status) {
            log('=== 撤销/重做状态信息 ===');
            log(`命令历史可用: ${status.commandHistoryAvailable}`);
            if (status.commandHistory) {
                log(`可撤销: ${status.commandHistory.canUndo}`);
                log(`可重做: ${status.commandHistory.canRedo}`);
                log(`撤销栈大小: ${status.commandHistory.undoCount}`);
                log(`重做栈大小: ${status.commandHistory.redoCount}`);
            }
            log(`剪贴板可用: ${status.clipboardAvailable}`);
            if (status.clipboard) {
                log(`剪贴板为空: ${status.clipboard.isEmpty}`);
                log(`节点数量: ${status.clipboard.nodeCount}`);
            }
            log('=== 状态信息结束 ===');
            setStatus('状态检查完成，详情请查看日志', 'success');
        }

        function checkUndoRedoStatus() {
            try {
                if (!mainAppWindow || mainAppWindow.closed) {
                    log('主应用窗口未打开或已关闭，请先打开主应用');
                    setStatus('请先打开主应用', 'error');
                    return;
                }

                if (!isMainAppReady) {
                    log('主应用尚未准备就绪，请稍候再试');
                    setStatus('主应用尚未准备就绪', 'error');
                    return;
                }

                // 发送消息请求状态检查
                mainAppWindow.postMessage({
                    type: 'check-status',
                    source: 'test-page'
                }, '*');

                log('已发送状态检查请求');
                setStatus('正在检查状态...', 'info');

            } catch (error) {
                log(`检查状态失败: ${error.message}`, 'error');
                setStatus('状态检查失败', 'error');
            }
        }

        function testBatchDelete() {
            try {
                if (!mainAppWindow || mainAppWindow.closed) {
                    log('主应用窗口未打开或已关闭，请先打开主应用');
                    setStatus('请先打开主应用', 'error');
                    return;
                }

                if (!isMainAppReady) {
                    log('主应用尚未准备就绪，请稍候再试');
                    setStatus('主应用尚未准备就绪', 'error');
                    return;
                }

                log('开始自动测试批量删除功能...');
                setStatus('正在进行自动测试...', 'info');

                // 发送自动测试请求
                mainAppWindow.postMessage({
                    type: 'auto-test-batch-delete',
                    source: 'test-page'
                }, '*');

                log('已发送自动测试请求');

            } catch (error) {
                log(`自动测试失败: ${error.message}`, 'error');
                setStatus('自动测试失败', 'error');
            }
        }

        function clearLog() {
            logElement.textContent = '测试日志已清空...\n';
            setStatus('日志已清空', 'info');
        }

        // 页面加载时的初始化
        window.addEventListener('load', function() {
            log('测试页面已加载');
            setStatus('测试页面已准备就绪', 'success');
        });

        // 检查主应用窗口状态
        function checkMainAppStatus() {
            if (mainAppWindow && mainAppWindow.closed) {
                log('检测到主应用窗口已关闭');
                setStatus('主应用窗口已关闭', 'error');
                mainAppWindow = null;
                isMainAppReady = false;
            }
        }

        // 定期检查主应用状态
        setInterval(checkMainAppStatus, 2000);
    </script>
</body>
</html>
