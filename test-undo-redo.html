<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试撤销/重做功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background: #e9ecef;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>撤销/重做功能测试</h1>
    
    <div class="test-container">
        <h2>测试说明</h2>
        <p>这个页面用于测试JointJS应用的撤销/重做功能，特别是批量删除操作的撤销功能。</p>
        <p><strong>测试步骤：</strong></p>
        <ol>
            <li>点击"打开主应用"按钮打开JointJS应用</li>
            <li>在主应用中添加3个节点到画布</li>
            <li>使用多选功能选中所有3个节点</li>
            <li>按Delete键或使用删除功能删除选中的节点</li>
            <li>按Ctrl+Z进行撤销操作</li>
            <li>验证所有3个节点是否正确恢复</li>
        </ol>
    </div>

    <div class="test-container">
        <h2>快速测试</h2>
        <button class="test-button" onclick="openMainApp()">打开主应用</button>
        <button class="test-button" onclick="checkUndoRedoStatus()">检查撤销/重做状态</button>
        <button class="test-button" onclick="testBatchDelete()">自动测试批量删除</button>
        <button class="test-button" onclick="clearLog()">清空日志</button>
    </div>

    <div class="test-container">
        <h2>测试状态</h2>
        <div id="status" class="status">等待测试...</div>
    </div>

    <div class="test-container">
        <h2>测试日志</h2>
        <div id="log" class="log">测试日志将显示在这里...\n</div>
    </div>

    <script>
        let logElement = document.getElementById('log');
        let statusElement = document.getElementById('status');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}\n`;
            logElement.textContent += logMessage;
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(message);
        }

        function setStatus(message, type = 'info') {
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        function openMainApp() {
            try {
                window.open('index.html', '_blank');
                log('已打开主应用窗口');
                setStatus('主应用已打开，请在主应用中进行测试', 'success');
            } catch (error) {
                log(`打开主应用失败: ${error.message}`, 'error');
                setStatus('打开主应用失败', 'error');
            }
        }

        function checkUndoRedoStatus() {
            try {
                // 尝试访问主窗口的API
                if (window.opener && window.opener.WorkflowAPI) {
                    const status = window.opener.WorkflowAPI.checkUndoRedoCopyPasteStatus();
                    log('撤销/重做状态检查完成，请查看控制台');
                    setStatus('状态检查完成，详情请查看控制台', 'success');
                } else {
                    log('无法访问主应用API，请确保主应用已打开');
                    setStatus('无法访问主应用，请先打开主应用', 'error');
                }
            } catch (error) {
                log(`检查状态失败: ${error.message}`, 'error');
                setStatus('状态检查失败', 'error');
            }
        }

        function testBatchDelete() {
            try {
                if (!window.opener || !window.opener.WorkflowAPI) {
                    log('无法访问主应用API，请确保主应用已打开');
                    setStatus('无法访问主应用，请先打开主应用', 'error');
                    return;
                }

                log('开始自动测试批量删除功能...');
                setStatus('正在进行自动测试...', 'info');

                // 这里可以添加自动化测试逻辑
                // 由于需要与JointJS交互，实际的自动化测试比较复杂
                // 目前提供手动测试指导
                
                log('自动测试功能开发中，请按照测试说明进行手动测试');
                setStatus('请进行手动测试', 'info');
                
            } catch (error) {
                log(`自动测试失败: ${error.message}`, 'error');
                setStatus('自动测试失败', 'error');
            }
        }

        function clearLog() {
            logElement.textContent = '测试日志已清空...\n';
            setStatus('日志已清空', 'info');
        }

        // 页面加载时的初始化
        window.addEventListener('load', function() {
            log('测试页面已加载');
            setStatus('测试页面已准备就绪', 'success');
        });

        // 监听来自主应用的消息
        window.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'test-result') {
                log(`收到测试结果: ${event.data.message}`);
                setStatus(event.data.message, event.data.success ? 'success' : 'error');
            }
        });
    </script>
</body>
</html>
